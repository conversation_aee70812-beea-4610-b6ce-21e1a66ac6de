import { defineConfig } from 'vite'

export default defineConfig({
  root: 'src',
  build: {
    outDir: '../dist',
    emptyOutDir: true,
    rollupOptions: {
      input: {
        main: 'src/pages/index.html',
        admin: 'src/pages/admin.html',
        courses: 'src/pages/courses.html',
        'course-detail': 'src/pages/course-detail.html',
        lesson: 'src/pages/lesson.html'
      }
    }
  },
  server: {
    open: '/pages/index.html'
  }
})
