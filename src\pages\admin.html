<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - IQA Interactive Quranic Arabic</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Crimson+Text:wght@400;600&family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
</head>
<body>
    <!-- Navigation Header -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <h1><a href="index.html">IQA</a></h1>
                <span class="nav-subtitle">INTERACTIVE QURANIC ARABIC</span>
            </div>
            <div class="nav-links">
                <a href="courses.html" class="nav-link">Courses</a>
                <a href="admin.html" class="nav-link active">Admin</a>
                <a href="about.html" class="nav-link">About Us</a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="admin-main">
        <div class="admin-container">
            <!-- Header Section -->
            <div class="admin-header">
                <div class="ornament-top">✦</div>
                <h1 class="page-title" id="mainPageTitle">Course Management</h1>
                <div class="title-underline"></div>
                <p class="page-subtitle" id="mainPageSubtitle">Create and manage courses for the IQA learning platform</p>
            </div>

            <!-- Course Creation Form -->
            <div class="form-section" id="courseManagementSection">
                <h2 class="section-title">Create New Course</h2>
                <form id="courseForm" class="course-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="courseTitle" class="form-label">Course Title</label>
                            <input type="text" id="courseTitle" name="title" class="form-input" placeholder="e.g., Beginner Quranic Arabic" required>
                        </div>
                        <div class="form-group">
                            <label for="courseLevel" class="form-label">Course Level</label>
                            <select id="courseLevel" name="level" class="form-select" required>
                                <option value="">Select Level</option>
                                <option value="beginner">Beginner</option>
                                <option value="intermediate">Intermediate</option>
                                <option value="advanced">Advanced</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="lessonCount" class="form-label">Number of Lessons</label>
                            <input type="number" id="lessonCount" name="lesson_count" class="form-input" min="1" max="50" placeholder="12" required>
                        </div>
                        <div class="form-group">
                            <label for="duration" class="form-label">Duration</label>
                            <input type="text" id="duration" name="duration_weeks" class="form-input" placeholder="e.g., 4-6 weeks" required>
                        </div>
                    </div>
                    
                    <div class="form-group full-width">
                        <label for="courseDescription" class="form-label">Course Description</label>
                        <textarea id="courseDescription" name="description" class="form-textarea" rows="4" 
                                  placeholder="Provide a detailed description of what students will learn in this course..." required></textarea>
                    </div>
                    
                    <div class="form-group checkbox-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="isPublished" name="is_published" class="form-checkbox">
                            <span class="checkmark"></span>
                            Publish course immediately (uncheck to keep as draft)
                        </label>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" id="resetForm" class="btn-secondary">Reset Form</button>
                        <button type="button" id="cancelCourseEditBtn" class="btn-secondary" style="display: none;">Cancel Edit</button>
                        <button type="submit" id="submitCourse" class="btn-primary">
                            <span class="btn-text">Create Course</span>
                            <span class="btn-arrow">→</span>
                        </button>
                    </div>
                </form>
            </div>

            <!-- Existing Courses Section -->
            <div class="courses-section">
                <h2 class="section-title">Existing Courses</h2>
                <div id="coursesList" class="courses-list">
                    <!-- Courses will be loaded dynamically -->
                </div>
            </div>

            <!-- Lesson Management Section -->
            <div class="lessons-management-section" id="lessonManagementSection" style="display: none;">
                <div class="section-header">
                    <h2 class="section-title">Lesson Management</h2>
                    <button class="btn-secondary" id="backToCoursesBtn">
                        <span class="btn-arrow">←</span>
                        <span class="btn-text">Back to Courses</span>
                    </button>
                </div>
                
                <div class="selected-course-info" id="selectedCourseInfo">
                    <!-- Selected course info will be displayed here -->
                </div>

                <!-- Lesson Creation Form -->
                <div class="form-section lesson-form-section">
                    <h3 class="subsection-title">Create New Lesson</h3>
                    <form id="lessonForm" class="lesson-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="lessonTitle" class="form-label">Lesson Title</label>
                                <input type="text" id="lessonTitle" name="title" class="form-input" placeholder="e.g., Introduction to Arabic Letters" required>
                            </div>
                            <div class="form-group">
                                <label for="lessonOrder" class="form-label">Lesson Order</label>
                                <input type="number" id="lessonOrder" name="lesson_order" class="form-input" min="1" placeholder="1" required>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="lessonDuration" class="form-label">Duration (minutes)</label>
                                <input type="number" id="lessonDuration" name="duration_minutes" class="form-input" min="5" max="120" placeholder="15" required>
                            </div>
                            <div class="form-group">
                                <!-- Empty group for layout balance -->
                            </div>
                        </div>
                        
                        <div class="form-group full-width">
                            <label for="lessonDescription" class="form-label">Lesson Description</label>
                            <textarea id="lessonDescription" name="description" class="form-textarea" rows="3" 
                                      placeholder="Provide a brief description of what students will learn in this lesson..."></textarea>
                        </div>

                        <!-- Material Builder Section (Integrated) -->
                        <div class="materials-section">
                            <h4 class="materials-title">Lesson Materials</h4>
                            <p class="materials-description">Build your lesson content using different material types. Materials will be displayed in the order you add them.</p>
                            
                            <div class="material-controls">
                                <button type="button" class="material-btn" data-type="text">+ Text Content</button>
                                <button type="button" class="material-btn" data-type="vocabulary">+ Vocabulary</button>
                                <button type="button" class="material-btn" data-type="image">+ Image</button>
                                <button type="button" class="material-btn" data-type="quiz_question">+ Quiz Question</button>
                            </div>
                            
                            <div class="materials-list" id="materialsList">
                                <p class="no-materials">No materials added yet. Use the buttons above to build your lesson content.</p>
                            </div>
                        </div>
                        
                        <div class="form-group checkbox-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="lessonPublished" name="is_published" class="form-checkbox">
                                <span class="checkmark"></span>
                                Publish lesson immediately
                            </label>
                        </div>
                        
                        <div class="form-actions">
                            <button type="button" id="resetLessonForm" class="btn-secondary">Reset Form</button>
                            <button type="button" id="cancelEditBtn" class="btn-secondary" style="display: none;">Cancel Edit</button>
                            <button type="submit" id="submitLesson" class="btn-primary">
                                <span class="btn-text">Create Lesson</span>
                                <span class="btn-arrow">→</span>
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Existing Lessons for Selected Course -->
                <div class="existing-lessons-section">
                    <h3 class="subsection-title">Existing Lessons</h3>
                    <div id="existingLessons" class="existing-lessons-list">
                        <!-- Lessons will be loaded dynamically -->
                    </div>
                </div>
            </div>

            <!-- Loading and Success/Error Messages -->
            <div id="loadingMessage" class="message loading-message" style="display: none;">
                <span class="loading-spinner">⟳</span>
                <span>Processing...</span>
            </div>
            
            <div id="successMessage" class="message success-message" style="display: none;">
                <span class="message-icon">✓</span>
                <span class="message-text"></span>
            </div>
            
            <div id="errorMessage" class="message error-message" style="display: none;">
                <span class="message-icon">⚠</span>
                <span class="message-text"></span>
            </div>
        </div>

        <!-- Lesson Preview Modal -->
        <div id="lessonPreviewModal" class="preview-modal" style="display: none;">
            <div class="preview-modal-overlay" onclick="closeLessonPreview()"></div>
            <div class="preview-modal-content">
                <div class="preview-modal-header">
                    <h2 class="preview-modal-title">👁️ Lesson Preview</h2>
                    <p class="preview-modal-subtitle">This is how the lesson will appear to students</p>
                    <button class="preview-close-btn" onclick="closeLessonPreview()">
                        <span>×</span>
                    </button>
                </div>
                
                <div class="preview-lesson-container">
                    <!-- Lesson Header -->
                    <div class="preview-lesson-header">
                        <!-- Lesson Title Section -->
                        <div class="preview-lesson-title-section">
                            <div class="preview-lesson-meta">
                                <span class="preview-course-title" id="previewCourseTitle">Course Name</span>
                                <span class="preview-lesson-duration" id="previewLessonDuration">15 min</span>
                            </div>
                            <h1 class="preview-lesson-title" id="previewLessonTitle">Lesson Title</h1>
                            <div class="preview-lesson-description" id="previewLessonDescription" style="display: none;">
                                <p id="previewLessonDescriptionText"></p>
                            </div>
                            <div class="preview-title-underline"></div>
                        </div>
                    </div>

                    <!-- Lesson Content Area -->
                    <div class="preview-lesson-content-area">
                        <!-- Loading State -->
                        <div class="preview-lesson-loading" id="previewLessonLoading">
                            <div class="loading-spinner">⟳</div>
                            <p>Loading lesson preview...</p>
                        </div>

                        <!-- Lesson Materials Container -->
                        <div class="preview-lesson-materials" id="previewLessonMaterials" style="display: none;">
                            <!-- Materials will be loaded dynamically -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="utils.js"></script>
    <script src="admin.js"></script>
</body>
</html>